import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Observable, Subscription } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';

import {
  CompanyDTO,
  StateDTO,
  ZipCodeDTO,
  ApiResponseCompanyDTO,
  CompanyRequestDTO,
} from '../../../api-client';
import { COMMON_STRINGS } from '../../../core/constants/common';
import { ICompanyFields } from '../../../core/interface/company-fields';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { CompanyValidation } from '../../../core/components/company-validation';
import { CompanyState } from '../../../core/components/company-state';
import { CompanyForm } from '../../../core/components/company-form';

@Injectable({
  providedIn: 'root',
})
export class EditCompanyFormService {
  constructor(
    private notification: NotificationService,
    private registerService: RegisterService,
    private companyStateService: CompanyState,
    private companyFormService: CompanyForm,
    private companyValidationService: CompanyValidation,
  ) {}

  async initializeForm(
    companyForm: FormGroup,
    company: ICompanyFields,
    states: StateDTO[],
    postalCode: ZipCodeDTO[],
    billingZipcode: ZipCodeDTO[],
    isSameAsCompanyDetails: boolean,
  ): Promise<{
    states: StateDTO[];
    postalCode: ZipCodeDTO[];
    billingZipcode: ZipCodeDTO[];
  }> {
    const fetchedStates = await this.fetchStates();
    this.populateFormWithCompanyData(companyForm, company, fetchedStates);
    
    const { primaryZipcodes, billingZipcodes } = await this.loadZipcodesForStates(
      companyForm,
      isSameAsCompanyDetails,
    );
    
    this.initializePostalCodeFields(companyForm);
    
    companyForm.markAsPristine();
    companyForm.markAsUntouched();
    
    return {
      states: fetchedStates,
      postalCode: primaryZipcodes,
      billingZipcode: billingZipcodes,
    };
  }

  private async loadZipcodesForStates(
    companyForm: FormGroup,
    isSameAsCompanyDetails: boolean,
  ): Promise<{ primaryZipcodes: ZipCodeDTO[]; billingZipcodes: ZipCodeDTO[] }> {
    const primaryStateId = companyForm.get('state')?.value;
    const billingStateId = companyForm.get('billingState')?.value;

    const promises = [];
    let primaryZipcodes: ZipCodeDTO[] = [];
    let billingZipcodes: ZipCodeDTO[] = [];

    if (primaryStateId) {
      promises.push(
        this.fetchZipcodes(primaryStateId).then((zipcodes) => {
          primaryZipcodes = zipcodes;
        }),
      );
    }

    if (billingStateId && !isSameAsCompanyDetails) {
      promises.push(
        this.fetchZipcodes(billingStateId).then((zipcodes) => {
          billingZipcodes = zipcodes;
        }),
      );
    }

    await Promise.all(promises);
    return { primaryZipcodes, billingZipcodes };
  }

  private populateFormWithCompanyData(
    companyForm: FormGroup,
    company: ICompanyFields,
    states: StateDTO[],
  ): void {
    this.companyFormService.populateFormWithCompanyData(
      companyForm,
      company,
      states,
    );
    this.ensureValidFormValues(companyForm, states);
  }

  private ensureValidFormValues(companyForm: FormGroup, states: StateDTO[]): void {
    const defaultStateId = states[0]?.id || null;
    const updates: Record<string, unknown> = {
      name: companyForm.get('name')?.value || '',
      abn: companyForm.get('abn')?.value || '',
      acn: companyForm.get('acn')?.value || '',
      addressLine1: companyForm.get('addressLine1')?.value || '',
      state: companyForm.get('state')?.value || defaultStateId,
      billingAddressLine1: companyForm.get('billingAddressLine1')?.value || '',
      billingState: companyForm.get('billingState')?.value || defaultStateId,
    };
    
    Object.keys(updates).forEach((key) => {
      const control = companyForm.get(key);
      if (control?.invalid) {
        control.setValue(updates[key], { emitEvent: false });
      }
    });
  }

  setupFormSubscriptions(
    companyForm: FormGroup,
    subscriptions: Subscription,
    onStateChange: (stateId: number, addressType: 'primary' | 'billing') => Promise<void>,
    onPostalCodeFieldStateChange: (fieldName: string, stateId: number | null) => void,
    onFormChange: () => void,
  ): void {
    subscriptions.add(
      companyForm.get('state')?.valueChanges.subscribe((stateId) => {
        onPostalCodeFieldStateChange('postalCode', stateId);
        if (stateId) onStateChange(stateId, 'primary');
      }),
    );

    subscriptions.add(
      companyForm.get('billingState')?.valueChanges.subscribe((stateId) => {
        onPostalCodeFieldStateChange('billingPostalCode', stateId);
        if (stateId) onStateChange(stateId, 'billing');
      }),
    );

    subscriptions.add(
      companyForm.valueChanges.subscribe(() => {
        onFormChange();
      }),
    );
  }

  async fetchStates(): Promise<StateDTO[]> {
    try {
      const states = await this.companyFormService.fetchStates().toPromise();
      const result = states || [];
      if (result.length === 0) {
        this.notification.error('No states available. Please try again later.');
      }
      return result;
    } catch (error) {
      console.error('Failed to fetch states:', error);
      this.notification.error('Failed to load states. Please try again.');
      return [];
    }
  }

  async fetchZipcodes(stateId: number): Promise<ZipCodeDTO[]> {
    if (!stateId) return [];

    try {
      const zipcodes = await this.companyFormService
        .fetchZipcodes(stateId)
        .toPromise();
      const result = zipcodes || [];
      if (result.length === 0) {
        this.notification.warning(
          COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
        );
      }
      return result;
    } catch (error) {
      console.error(`Failed to fetch zipcodes:`, error);
      this.notification.error(`Failed to load zipcodes. Please try again.`);
      return [];
    }
  }

  processZipcodes(
    zipcodes: ZipCodeDTO[],
    addressType: 'primary' | 'billing',
    company: ICompanyFields | null,
    companyForm: FormGroup,
  ): void {
    const zipCodeId =
      addressType === 'primary'
        ? company?.primaryAddress?.zipCodeId
        : company?.billingAddress?.zipCodeId;
    
    this.setMatchingZipcode(
      addressType === 'primary' ? 'postalCode' : 'billingPostalCode',
      zipCodeId,
      zipcodes,
      companyForm,
    );
  }

  private setMatchingZipcode(
    controlName: string,
    zipCodeId: number | undefined,
    zipcodes: ZipCodeDTO[],
    companyForm: FormGroup,
  ): void {
    const defaultZipcode = zipcodes[0]?.id || '';
    if (zipCodeId) {
      const matchingZipcode = zipcodes.find((z) => z.id === zipCodeId);
      if (matchingZipcode) {
        companyForm
          .get(controlName)
          ?.setValue(matchingZipcode.id, { emitEvent: false });
      } else {
        console.warn(
          `No matching zipcode found for ${controlName} with id ${zipCodeId}`,
        );
        companyForm
          .get(controlName)
          ?.setValue(defaultZipcode, { emitEvent: false });
      }
    } else {
      companyForm
        .get(controlName)
        ?.setValue(defaultZipcode, { emitEvent: false });
    }
  }

  resetPostalCodeForAddressType(
    addressType: 'primary' | 'billing',
    companyForm: FormGroup,
    postalCode: ZipCodeDTO[],
    billingZipcode: ZipCodeDTO[],
  ): void {
    const controlName =
      addressType === 'primary' ? 'postalCode' : 'billingPostalCode';
    const defaultZipcode =
      (addressType === 'primary' ? postalCode : billingZipcode)[0]?.id || '';
    companyForm
      .get(controlName)
      ?.setValue(defaultZipcode, { emitEvent: false });
  }

  syncBillingZipcodes(
    companyForm: FormGroup,
    postalCode: ZipCodeDTO[],
  ): { billingZipcode: ZipCodeDTO[]; primaryPostalCode: string } {
    const primaryPostalCode =
      companyForm.get('postalCode')?.value || postalCode[0]?.id || '';
    companyForm
      .get('billingPostalCode')
      ?.setValue(primaryPostalCode, { emitEvent: false });
    return { billingZipcode: postalCode, primaryPostalCode };
  }

  handlePostalCodeFieldState(
    fieldName: string,
    stateId: number | null,
    companyForm: FormGroup,
  ): void {
    const postalCodeControl = companyForm.get(fieldName);
    if (postalCodeControl) {
      if (!stateId) {
        postalCodeControl.setValue('', { emitEvent: false });
        postalCodeControl.disable({ emitEvent: false });
      } else {
        postalCodeControl.enable({ emitEvent: false });
      }
    }
  }

  initializePostalCodeFields(companyForm: FormGroup): void {
    const primaryStateId = companyForm.get('state')?.value;
    const billingStateId = companyForm.get('billingState')?.value;

    this.handlePostalCodeFieldState('postalCode', primaryStateId, companyForm);
    this.handlePostalCodeFieldState('billingPostalCode', billingStateId, companyForm);
  }

  saveCompanyDetails(
    companyForm: FormGroup,
    company: ICompanyFields,
    isSameAsCompanyDetails: boolean,
    states: StateDTO[],
    postalCode: ZipCodeDTO[],
    billingZipcode: ZipCodeDTO[],
  ): Observable<ApiResponseCompanyDTO> {
    if (!companyForm.valid) {
      this.handleInvalidForm(companyForm);
      throw new Error('Form is invalid');
    }

    if (!company?.id) {
      throw new Error('Company ID is required');
    }

    const tempCompanyRequest = this.buildCompanyRequest(
      companyForm,
      company,
      isSameAsCompanyDetails,
    );

    return this.registerService.updateCompany(company.id, tempCompanyRequest);
  }

  buildCompanyRequest(
    companyForm: FormGroup,
    company: ICompanyFields | null,
    isSameAsCompanyDetails: boolean,
  ): CompanyRequestDTO {
    const formValue = companyForm.getRawValue();
    return this.companyFormService.buildCompanyRequest(
      formValue,
      company,
      isSameAsCompanyDetails,
    );
  }

  buildTempCompany(
    tempCompanyRequest: CompanyRequestDTO,
    company: ICompanyFields | null,
    states: StateDTO[],
    postalCode: ZipCodeDTO[],
    billingZipcode: ZipCodeDTO[],
  ): ICompanyFields {
    return this.companyFormService.buildTempCompany(
      tempCompanyRequest,
      company,
      states,
      postalCode,
      billingZipcode,
      this.companyStateService.getTempCompanyData(),
    );
  }

  handleUpdateSuccess(
    response: ApiResponseCompanyDTO,
    tempCompany: ICompanyFields,
    onSuccess: (updatedCompany: ICompanyFields) => void,
  ): void {
    if (response.success && response.data) {
      const updatedCompany = { ...tempCompany, ...response.data };
      this.companyStateService.setCompanyData(updatedCompany);
      this.companyStateService.clearTempCompanyData();
      this.notification.success(
        COMMON_STRINGS.successMessages.companyUpdateSuccess,
      );
      onSuccess(updatedCompany);
    } else {
      this.handleUpdateFailure(response.message);
    }
  }

  private handleUpdateFailure(message?: string): void {
    this.notification.error(
      COMMON_STRINGS.warningMessages.companyUpdateFailure.replace(
        '${errorMessage}',
        message || '',
      ),
    );
  }

  handleUpdateError(error: HttpErrorResponse): void {
    const errorMessage = error.error?.message || error.message;
    this.notification.error(
      COMMON_STRINGS.warningMessages.companyUpdateFailure.replace(
        '${errorMessage}',
        errorMessage,
      ),
    );
  }

  private handleInvalidForm(companyForm: FormGroup): void {
    this.companyValidationService.handleInvalidForm(companyForm);
    const invalidFields = Object.keys(companyForm.controls)
      .filter((key) => companyForm.get(key)?.invalid)
      .join(', ');
    this.notification.error(
      invalidFields
        ? `Please fill in all required fields: ${invalidFields}`
        : COMMON_STRINGS.warningMessages.enterAllFields,
    );
  }

  resetFormState(
    companyForm: FormGroup,
    onReset: () => void,
  ): void {
    this.companyStateService.clearTempCompanyData();
    companyForm.reset();
    companyForm.markAsPristine();
    onReset();
  }

  handleSameAsCompanyDetailsChange(
    checked: boolean,
    companyForm: FormGroup,
    postalCode: ZipCodeDTO[],
  ): { billingZipcode: ZipCodeDTO[] } {
    if (checked) {
      const primaryValues = this.companyFormService.copyPrimaryToBillingAddress(
        companyForm,
      );
      companyForm.patchValue(primaryValues, { emitEvent: false });
      this.companyFormService.manageBillingFields(companyForm, true);
      return { billingZipcode: postalCode };
    } else {
      this.companyFormService.manageBillingFields(companyForm, false);
      return { billingZipcode: [] };
    }
  }

  getCompanyErrorTip(companyForm: FormGroup, controlName: string): string | undefined {
    return this.companyValidationService.getCompanyErrorTip(
      companyForm,
      controlName,
    );
  }

  isCompanyDataUnchanged(
    original: ICompanyFields,
    updated: ICompanyFields,
  ): boolean {
    return this.companyValidationService.isCompanyDataUnchanged(
      original,
      updated,
    );
  }

  isSaveButtonEnabled(companyForm: FormGroup, isLoading: boolean): boolean {
    return this.companyValidationService.isSaveButtonEnabled(
      companyForm,
      isLoading,
    );
  }
}
