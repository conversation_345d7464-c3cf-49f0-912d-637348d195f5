import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, ReactiveFormsModule, FormsModule } from '@angular/forms';

import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzUploadModule } from 'ng-zorro-antd/upload';

import { Subscription } from 'rxjs';

import {
  CompanyDTO,
  StateDTO,
  ZipCodeDTO,
} from '../../../api-client';
import { ICompanyFields } from '../../../core/interface/company-fields';
import { CompanyForm } from '../../../core/components/company-form';
import { EditCompanyFormService } from './edit-company-form.service';

@Component({
  selector: 'app-edit-company-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NzTabsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzGridModule,
    NzCheckboxModule,
    NzCardModule,
    NzTableModule,
    NzModalModule,
    NzUploadModule,
    NzIconModule,
    NzDrawerModule,
    NzSelectModule,
    NzBreadCrumbModule,
  ],
  templateUrl: './edit-company-form.component.html',
  styleUrls: ['./edit-company-form.component.scss'],
})
export class EditCompanyFormComponent implements OnInit, OnDestroy {
  @Input() isVisible = false;
  @Input() company: ICompanyFields | null = null;
  @Output() companySaved = new EventEmitter<CompanyDTO>();
  @Output() formCancelled = new EventEmitter<void>();

  companyForm: FormGroup;
  isLoading = false;
  isBillingDetailsAccordionOpen = false;
  isSameAsCompanyDetails = false;

  states: StateDTO[] = [];
  postalCode: ZipCodeDTO[] = [];
  billingZipcode: ZipCodeDTO[] = [];

  private subscriptions = new Subscription();

  constructor(
    private cdr: ChangeDetectorRef,
    private companyFormService: CompanyForm,
    private editCompanyFormService: EditCompanyFormService,
  ) {
    this.companyForm = this.companyFormService.createEditCompanyForm();
  }

  async ngOnInit(): Promise<void> {
    if (this.company) {
      await this.initializeForm();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  get isSaveButtonEnabled(): boolean {
    return this.editCompanyFormService.isSaveButtonEnabled(
      this.companyForm,
      this.isLoading,
    );
  }

  async initializeForm(): Promise<void> {
    if (!this.company) return;

    const result = await this.editCompanyFormService.initializeForm(
      this.companyForm,
      this.company,
      this.states,
      this.postalCode,
      this.billingZipcode,
      this.isSameAsCompanyDetails,
    );

    this.states = result.states;
    this.postalCode = result.postalCode;
    this.billingZipcode = result.billingZipcode;

    this.setupFormSubscriptions();
    this.cdr.detectChanges();
  }



  private setupFormSubscriptions(): void {
    this.editCompanyFormService.setupFormSubscriptions(
      this.companyForm,
      this.subscriptions,
      (stateId, addressType) => this.handleStateChange(stateId, addressType),
      (fieldName, stateId) => this.handlePostalCodeFieldState(fieldName, stateId),
      () => this.cdr.detectChanges(),
    );
  }

  private async handleStateChange(
    stateId: number,
    addressType: 'primary' | 'billing',
  ): Promise<void> {
    const zipcodes = await this.editCompanyFormService.fetchZipcodes(stateId);

    if (addressType === 'primary') {
      this.postalCode = zipcodes;
    } else {
      this.billingZipcode = zipcodes;
    }

    this.editCompanyFormService.processZipcodes(
      zipcodes,
      addressType,
      this.company,
      this.companyForm,
    );

    this.editCompanyFormService.resetPostalCodeForAddressType(
      addressType,
      this.companyForm,
      this.postalCode,
      this.billingZipcode,
    );

    if (addressType === 'primary' && this.isSameAsCompanyDetails) {
      const result = this.editCompanyFormService.syncBillingZipcodes(
        this.companyForm,
        this.postalCode,
      );
      this.billingZipcode = result.billingZipcode;
    }

    this.cdr.detectChanges();
  }

  private handlePostalCodeFieldState(
    fieldName: string,
    stateId: number | null,
  ): void {
    this.editCompanyFormService.handlePostalCodeFieldState(
      fieldName,
      stateId,
      this.companyForm,
    );
  }

  saveCompanyDetails(): void {
    this.isLoading = true;

    try {
      this.editCompanyFormService
        .saveCompanyDetails(
          this.companyForm,
          this.company!,
          this.isSameAsCompanyDetails,
          this.states,
          this.postalCode,
          this.billingZipcode,
        )
        .subscribe({
          next: (response) => {
            const tempCompanyRequest = this.editCompanyFormService.buildCompanyRequest(
              this.companyForm.getRawValue(),
              this.company,
              this.isSameAsCompanyDetails,
            );
            const tempCompany = this.editCompanyFormService.buildTempCompany(
              tempCompanyRequest,
              this.company,
              this.states,
              this.postalCode,
              this.billingZipcode,
            );

            this.editCompanyFormService.handleUpdateSuccess(
              response,
              tempCompany,
              (updatedCompany) => {
                this.companySaved.emit(updatedCompany);
              },
            );
          },
          error: (error) => {
            this.editCompanyFormService.handleUpdateError(error);
          },
          complete: () => {
            this.isLoading = false;
            this.cdr.detectChanges();
          },
        });
    } catch (error) {
      this.isLoading = false;
      this.cdr.detectChanges();
    }
  }

  cancelEdit(): void {
    this.editCompanyFormService.resetFormState(this.companyForm, () => {
      this.isSameAsCompanyDetails = false;
      this.isBillingDetailsAccordionOpen = false;
      this.cdr.detectChanges();
    });
    this.formCancelled.emit();
  }



  onSameAsCompanyDetailsChange(checked: boolean): void {
    this.isSameAsCompanyDetails = checked;
    const result = this.editCompanyFormService.handleSameAsCompanyDetailsChange(
      checked,
      this.companyForm,
      this.postalCode,
    );
    this.billingZipcode = result.billingZipcode;
    this.companyForm.markAsDirty();
    this.cdr.detectChanges();
  }

  getCompanyErrorTip(controlName: string): string | undefined {
    return this.editCompanyFormService.getCompanyErrorTip(
      this.companyForm,
      controlName,
    );
  }

  isCompanyDataUnchanged(
    original: ICompanyFields,
    updated: ICompanyFields,
  ): boolean {
    return this.editCompanyFormService.isCompanyDataUnchanged(original, updated);
  }

  toggleBillingDetailsAccordion(): void {
    this.isBillingDetailsAccordionOpen = !this.isBillingDetailsAccordionOpen;
    this.cdr.detectChanges();
  }

}
