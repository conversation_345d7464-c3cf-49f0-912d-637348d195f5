import { Injectable } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { <PERSON><PERSON>anitizer, SafeUrl } from '@angular/platform-browser';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { RoleDTO, UserRequestDTO, UserResponseDTO } from '../../../api-client';
import { PHONE_REGEX, COMMON_STRINGS } from '../../../core/constants/common';
import { IUserFields } from '../../../core/interface/user-fields';
import { ICompanyFields } from '../../../core/interface/company-fields';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { CompanyState } from '../../../core/components/company-state';
import { SharedLookup } from '../../../core/components/shared-lookup';
import {
  UserCreation,
  UserCreationRequest,
  UploadResult,
} from '../../../core/components/user-creation';
import { UserRequestMapper } from '../../../core/components/user-form';

@Injectable({
  providedIn: 'root',
})
export class AddUserService {
  constructor(
    private fb: FormBuilder,
    private notification: NotificationService,
    private sharedLookupService: SharedLookup,
    private companyStateService: CompanyState,
    private userCreationService: UserCreation,
    private registerService: RegisterService,
    private userRequestMapper: UserRequestMapper,
    private sanitizer: DomSanitizer,
  ) {}

  createUserForm(): FormGroup {
    return this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      contactNumber: [
        '',
        [Validators.required, Validators.pattern(PHONE_REGEX)],
      ],
      roleId: [null, Validators.required],
      profilePictureUrl: [null],
    });
  }

  getProfileImageSrc(addUserForm: FormGroup): SafeUrl | string {
    const profilePictureUrl = addUserForm.get('profilePictureUrl')?.value;
    if (profilePictureUrl && profilePictureUrl.startsWith('data:image')) {
      return this.sanitizer.bypassSecurityTrustUrl(profilePictureUrl);
    }
    return profilePictureUrl || 'assets/profileImage.png';
  }

  populateFormWithUserData(
    addUserForm: FormGroup,
    userToEdit: IUserFields,
  ): IUserFields {
    if (!userToEdit) {
      console.error('AddUserService: No userToEdit provided for population');
      return {};
    }
    
    const formData = {
      firstName: userToEdit.firstName || '',
      lastName: userToEdit.lastName || '',
      email: userToEdit.email || '',
      contactNumber: userToEdit.contactNumber || '',
      roleId: userToEdit.roleId || null,
      profilePictureUrl: userToEdit.profilePictureUrl || null,
    };
    
    addUserForm.patchValue(formData);
    addUserForm.markAsPristine();
    
    return { ...addUserForm.value };
  }

  checkFormChanges(
    addUserForm: FormGroup,
    originalFormValue: IUserFields | undefined,
    selectedFile: File | null,
    isEditMode: boolean,
  ): boolean {
    if (isEditMode && originalFormValue) {
      const currentValue: IUserFields = addUserForm.value;
      const hasChanges =
        Object.keys(originalFormValue).some(
          (key) => currentValue[key] !== originalFormValue[key],
        ) || selectedFile !== null;
      
      if (!hasChanges) {
        addUserForm.markAsPristine();
        return false;
      } else {
        addUserForm.markAsDirty();
        return true;
      }
    }
    return false;
  }

  fetchRoles(): Observable<RoleDTO[]> {
    return this.sharedLookupService.fetchRoles().pipe(
      map((roles: RoleDTO[]) => {
        return roles;
      }),
      catchError(() => {
        this.notification.error(
          COMMON_STRINGS.errorMessages.displayRolesFailed,
        );
        return of([]);
      }),
    );
  }

  isAddUserFormValid(
    addUserForm: FormGroup,
    companyId: number | undefined,
    isEditMode: boolean,
  ): boolean {
    if (companyId == null && !isEditMode) {
      return false;
    }
    return addUserForm.valid;
  }

  handleInvalidAddUserForm(addUserForm: FormGroup): void {
    addUserForm.markAllAsTouched();
    this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
  }

  createUser(
    addUserForm: FormGroup,
    companyId: number,
    company: ICompanyFields | null | undefined,
    selectedFile: File | null,
  ): Observable<any> {
    const request: UserCreationRequest = {
      formData: addUserForm.value,
      companyId: companyId,
      company: company || undefined,
      selectedFile: selectedFile,
      userType: 'COMPANY',
    };

    return this.userCreationService.createUserWithProfilePicture(request);
  }

  updateUser(
    addUserForm: FormGroup,
    userToEdit: IUserFields,
    company: ICompanyFields | null | undefined,
    companyId: number | undefined,
    selectedFile: File | null,
    roles: RoleDTO[],
  ): Observable<any> {
    if (!userToEdit?.id) {
      this.notification.error(COMMON_STRINGS.errorMessages.userIdNotFound);
      throw new Error('User ID not found');
    }

    const userRequest = this.userRequestMapper.createUserRequest(
      addUserForm,
      company,
      companyId,
      userToEdit.isActive,
    );

    if (!userRequest) {
      this.notification.error(
        COMMON_STRINGS.errorMessages.companyAddressRequired,
      );
      throw new Error('Company address required');
    }

    if (selectedFile) {
      return this.userCreationService
        .uploadProfilePicture(userRequest.email, selectedFile)
        .pipe(
          map((result: UploadResult) => {
            if (result.success && result.url) {
              userRequest.profilePictureUrl = result.url;
              return this.performUserUpdate(userToEdit.id!, userRequest, roles);
            } else {
              this.notification.error(
                result.error || 'Failed to upload profile picture',
              );
              throw new Error('Failed to upload profile picture');
            }
          }),
        );
    } else {
      return this.performUserUpdate(userToEdit.id!, userRequest, roles);
    }
  }

  private performUserUpdate(
    userId: number,
    userRequest: UserRequestDTO,
    roles: RoleDTO[],
  ): Observable<any> {
    return this.registerService.updateUser(userId, userRequest).pipe(
      map((response) => {
        if (response.success && response.data) {
          const updatedUser =
            this.userRequestMapper.mapUserResponseToUserFields(
              response.data,
              roles,
              this.companyStateService.getCompanyDetails(),
            );
          if (updatedUser) {
            this.notification.success(
              COMMON_STRINGS.successMessages.userUpdateSuccess,
            );
            return { success: true, userData: updatedUser };
          }
        }
        throw new Error('Failed to update user');
      }),
      catchError((error) => {
        console.error('Error updating user:', error);
        this.notification.error(
          COMMON_STRINGS.errorMessages.failedToUpdateUser,
        );
        throw error;
      }),
    );
  }

  handleCreateUserSuccess(
    userData: UserResponseDTO,
    roles: RoleDTO[],
    company: ICompanyFields | null | undefined,
    existingUsers: IUserFields[],
  ): IUserFields | null {
    if (!userData) {
      this.handleCreateUserError();
      return null;
    }

    const updatedUser = this.userRequestMapper.mapUserResponseToUserFields(
      userData,
      roles,
      company,
    );

    if (!updatedUser) {
      this.handleCreateUserError();
      return null;
    }

    existingUsers.unshift(updatedUser);
    return updatedUser;
  }

  private handleCreateUserError(): void {
    this.notification.error(COMMON_STRINGS.warningMessages.userCreationFailed);
  }

  getErrorTip(addUserForm: FormGroup, controlName: string): string | undefined {
    const control = addUserForm.get(controlName);
    if (control?.touched && control?.hasError('required')) {
      return `${controlName.charAt(0).toUpperCase() + controlName.slice(1)} is required`;
    }
    if (
      controlName === 'email' &&
      control?.touched &&
      control?.hasError('email')
    ) {
      return COMMON_STRINGS.warningMessages.enterValidEmail;
    }
    if (
      controlName === 'contactNumber' &&
      control?.touched &&
      control?.hasError('pattern')
    ) {
      return COMMON_STRINGS.warningMessages.enterValidPhoneNumber;
    }
    return undefined;
  }

  handleFileSelection(
    event: Event,
    onFileSelected: (file: File, dataUrl: string) => void,
  ): void {
    this.userCreationService.handleFileSelection(event, onFileSelected);
  }

  isUserFormValid(
    addUserForm: FormGroup,
    isEditMode: boolean,
    selectedFile: File | null,
    existingUsers: IUserFields[],
  ): boolean {
    if (isEditMode) {
      return (
        addUserForm.valid &&
        (addUserForm.dirty || selectedFile !== null)
      );
    }
    const email = addUserForm.get('email')?.value;
    return addUserForm.valid && (!email || this.isEmailUnique(email, existingUsers));
  }

  isEmailUnique(email: string, existingUsers: IUserFields[]): boolean {
    return !existingUsers.some(
      (user) => user.email?.toLowerCase() === email.toLowerCase(),
    );
  }

  resetForm(addUserForm: FormGroup): void {
    addUserForm.reset();
    addUserForm.markAsPristine();
  }
}
