import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { Observable, of, Subscription } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { RoleDTO, UserRequestDTO, UserResponseDTO } from '../../../api-client';
import { PHONE_REGEX, COMMON_STRINGS } from '../../../core/constants/common';
import { IUserFields } from '../../../core/interface/user-fields';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { CompanyState } from '../../../core/components/company-state';
import { SharedLookup } from '../../../core/components/shared-lookup';
import {
  UserCreation,
  UserCreationRequest,
  UploadResult,
} from '../../../core/components/user-creation';
import { UserRequestMapper } from '../../../core/components/user-form';
import { ICompanyFields } from '../../../core/interface/company-fields';
import { AddUserService } from './add-user.service';

@Component({
  selector: 'app-add-user',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzTabsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzGridModule,
    NzCheckboxModule,
    NzCardModule,
    NzTableModule,
    NzModalModule,
    NzUploadModule,
    NzIconModule,
    NzDrawerModule,
    NzSelectModule,
    NzBreadCrumbModule,
  ],
  templateUrl: './add-user.component.html',
  styleUrl: './add-user.component.scss',
})
export class AddUserComponent implements OnInit, OnDestroy {
  @Input() isVisible = false;
  @Input() companyId: number | undefined;
  @Input() company: ICompanyFields | null | undefined = null;
  @Input() existingUsers: IUserFields[] = [];
  @Output() userAdded = new EventEmitter<IUserFields>();
  @Output() userUpdated = new EventEmitter<IUserFields>();
  @Output() drawerClosed = new EventEmitter<void>();
  @Input() showBreadcrumbs = false;

  @Input() set userToEdit(value: IUserFields | undefined) {
    this._userToEdit = value;
    if (value) {
      this.isEditMode = true;
      this.populateFormWithUserData();
    } else {
      this.isEditMode = false;
      this.addUserForm.reset();
    }
  }
  get userToEdit(): IUserFields | undefined {
    return this._userToEdit;
  }

  addUserForm: FormGroup;
  isLoading = false;
  roles$: Observable<RoleDTO[]> = of([]);
  roles: RoleDTO[] = [];
  selectedFile: File | null = null;
  isEditMode = false;
  private fileSelectedSubscription: Subscription = new Subscription();
  private originalFormValue?: IUserFields;
  private _userToEdit?: IUserFields;

  constructor(
    private addUserService: AddUserService,
    private sanitizer: DomSanitizer,
  ) {
    this.addUserForm = this.addUserService.createUserForm();
  }

  get profileImageSrc(): SafeUrl | string {
    return this.addUserService.getProfileImageSrc(this.addUserForm);
  }

  ngOnInit(): void {
    this.fetchRoles();
    if (this.userToEdit) {
      this.isEditMode = true;
      this.populateFormWithUserData();
    }
    this.addUserForm.valueChanges.subscribe(() => {
      this.checkFormChanges();
    });
  }

  ngOnDestroy(): void {
    if (this.fileSelectedSubscription) {
      this.fileSelectedSubscription.unsubscribe();
    }
  }

  private populateFormWithUserData(): void {
    if (this.userToEdit) {
      this.originalFormValue = this.addUserService.populateFormWithUserData(
        this.addUserForm,
        this.userToEdit,
      );
    }
  }

  private checkFormChanges(): void {
    this.addUserService.checkFormChanges(
      this.addUserForm,
      this.originalFormValue,
      this.selectedFile,
      this.isEditMode,
    );
  }

  toggleAddUserDrawer(): void {
    if (this.isVisible) {
      this.drawerClosed.emit();
    }
    this.addUserForm.reset();
    this.selectedFile = null;
    this.isEditMode = false;
    this.originalFormValue = undefined;
  }

  fetchRoles(): Observable<RoleDTO[]> {
    this.isLoading = true;
    this.roles$ = this.addUserService.fetchRoles().pipe(
      map((roles: RoleDTO[]) => {
        this.isLoading = false;
        this.roles = roles;
        return roles;
      }),
    );
    return this.roles$;
  }

  submitForm(): void {
    if (!this.isAddUserFormValid()) {
      this.handleInvalidAddUserForm();
      return;
    }
    this.isLoading = true;
    if (this.isEditMode) {
      this.updateUser();
    } else {
      this.addUser();
    }
  }

  private addUser(): void {
    this.addUserService
      .createUser(
        this.addUserForm,
        this.companyId!,
        this.company,
        this.selectedFile,
      )
      .subscribe({
        next: (result) => {
          if (result.success && result.userData) {
            this.handleCreateUserSuccess(result.userData);
            this.selectedFile = null;
            this.addUserService.resetForm(this.addUserForm);
          }
        },
        error: (error) => {
          console.error('Error creating user:', error);
          this.isLoading = false;
        },
        complete: () => {
          this.isLoading = false;
        },
      });
  }

  private updateUser(): void {
    const userToEdit: IUserFields | undefined = this.userToEdit;
    if (!userToEdit?.id) {
      this.isLoading = false;
      return;
    }

    this.addUserService
      .updateUser(
        this.addUserForm,
        userToEdit,
        this.company,
        this.companyId,
        this.selectedFile,
        this.roles,
      )
      .subscribe({
        next: (result) => {
          if (result.success && result.userData) {
            this.handleCreateUserSuccess(result.userData);
            this.selectedFile = null;
          }
        },
        error: (error) => {
          console.error('Error updating user:', error);
          this.isLoading = false;
        },
        complete: () => {
          this.isLoading = false;
        },
      });
  }

  private performUserUpdate(userId: number, userRequest: UserRequestDTO): void {
    this.registerService.updateUser(userId, userRequest).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          const updatedUser =
            this.userRequestMapper.mapUserResponseToUserFields(
              response.data,
              this.roles,
              this.companyStateService.getCompanyDetails(),
            );
          if (updatedUser) {
            this.userUpdated.emit(updatedUser);
            this.notification.success(
              COMMON_STRINGS.successMessages.userUpdateSuccess,
            );
          }
        }
        this.isLoading = false;
        this.drawerClosed.emit();
      },
      error: (error) => {
        console.error('Error updating user:', error);
        this.notification.error(
          COMMON_STRINGS.errorMessages.failedToUpdateUser,
        );
        this.isLoading = false;
      },
    });
  }

  private isAddUserFormValid(): boolean {
    if (this.companyId == null && !this.isEditMode) {
      return false;
    }
    return this.addUserForm.valid;
  }

  private handleInvalidAddUserForm(): void {
    this.addUserForm.markAllAsTouched();
    this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
  }

  private handleCreateUserSuccess(userData: UserResponseDTO): void {
    const updatedUser = this.addUserService.handleCreateUserSuccess(
      userData,
      this.roles,
      this.company,
      this.existingUsers,
    );

    if (updatedUser) {
      this.addUserService.resetForm(this.addUserForm);
      this.toggleAddUserDrawer();
      this.isLoading = false;
      this.userAdded.emit(updatedUser);
    } else {
      this.isLoading = false;
    }
  }

  getErrorTip(controlName: string): string | undefined {
    return this.addUserService.getErrorTip(this.addUserForm, controlName);
  }

  onFileSelected(event: Event): void {
    this.addUserService.handleFileSelection(
      event,
      (file: File, dataUrl: string) => {
        this.selectedFile = file;
        this.addUserForm.get('profilePictureUrl')?.setValue(dataUrl);
        this.addUserForm.markAsDirty();
      },
    );
  }

  isUserFormValid(): boolean {
    return this.addUserService.isUserFormValid(
      this.addUserForm,
      this.isEditMode,
      this.selectedFile,
      this.existingUsers,
    );
  }

  isEmailUnique(email: string): boolean {
    return this.addUserService.isEmailUnique(email, this.existingUsers);
  }

  onImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;
    imgElement.src = 'assets/profileImage.png';
    imgElement.alt = 'Default profile image';
  }

  navigateToManageUsers(): void {
    this.toggleAddUserDrawer();
  }
}
