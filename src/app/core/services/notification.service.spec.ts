import { TestBed } from '@angular/core/testing';
import { NotificationService } from './notification.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzMessageService } from 'ng-zorro-antd/message';
import { EnumNotificationMessageTypes } from '../enumerations/notificationMessage';
import { Subject } from 'rxjs';

describe('NotificationService', () => {
  let service: NotificationService;
  let notificationServiceSpy: jasmine.SpyObj<NzNotificationService>;
  let messageServiceSpy: jasmine.SpyObj<NzMessageService>;

  beforeEach(() => {
    const notificationSpy = jasmine.createSpyObj('NzNotificationService', [
      'create',
    ]);
    const messageSpy = jasmine.createSpyObj('NzMessageService', [
      'create',
      'loading',
      'remove',
    ]);

    TestBed.configureTestingModule({
      providers: [
        NotificationService,
        { provide: NzNotificationService, useValue: notificationSpy },
        { provide: NzMessageService, useValue: messageSpy },
      ],
    });

    service = TestBed.inject(NotificationService);
    notificationServiceSpy = TestBed.inject(
      NzNotificationService,
    ) as jasmine.SpyObj<NzNotificationService>;
    messageServiceSpy = TestBed.inject(
      NzMessageService,
    ) as jasmine.SpyObj<NzMessageService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Notification Methods', () => {
    it('should call notificationService.create with success type', () => {
      const content = 'Success Message';
      const defaultStyle = {
        background: '#d4edda',
        color: '#155724',
        border: '1px solid rgb(87 202 113)',
        padding: '16px 30px',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        height: '5rem',
        whiteSpace: 'nowrap !important',
        width: 'auto !important',
        maxWidth: 'none !important',
      };

      service.success(content);
      expect(notificationServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.success,
        '',
        content,
        jasmine.objectContaining({
          nzPlacement: 'bottomLeft',
          nzStyle: defaultStyle,
        }),
      );
    });

    it('should call notificationService.create with error type', () => {
      const content = 'Error Message';
      const defaultStyle = {
        background: '#f8d7da',
        color: '#721c24',
        border: '1px solid rgb(245 101 101)',
        padding: '16px 30px',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        height: '5rem',
      };

      service.error(content);
      expect(notificationServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.error,
        '',
        content,
        jasmine.objectContaining({
          nzPlacement: 'bottomLeft',
          nzStyle: defaultStyle,
        }),
      );
    });

    it('should call notificationService.create with warning type', () => {
      const content = 'Warning Message';
      const defaultStyle = {
        background: '#fff3cd',
        color: '#856404',
        border: '1px solid rgb(255 193 7)',
        padding: '16px 30px',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        height: '5rem',
      };

      service.warning(content);
      expect(notificationServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.warning,
        '',
        content,
        jasmine.objectContaining({
          nzPlacement: 'bottomLeft',
          nzStyle: defaultStyle,
        }),
      );
    });

    it('should call notificationService.create with info type', () => {
      const content = 'Info Message';
      const defaultStyle = {
        background: '#d1ecf1',
        color: '#0c5460',
        border: '1px solid rgb(102 209 226)',
        padding: '16px 30px',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        height: '5rem',
      };

      service.info(content);
      expect(notificationServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.info,
        '',
        content,
        jasmine.objectContaining({
          nzPlacement: 'bottomLeft',
          nzStyle: defaultStyle,
        }),
      );
    });

    it('should call notificationService.create with blank type', () => {
      const content = 'Generic Notification';
      service.blank(content);
      expect(notificationServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.blank,
        '',
        content,
        jasmine.objectContaining({
          nzPlacement: 'bottomLeft',
        }),
      );
    });
  });

  describe('Message Methods', () => {
    it('should call messageService.create with success type', () => {
      const content = 'Success Message';
      service.successMessage(content);
      expect(messageServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.success,
        content,
      );
    });

    it('should call messageService.create with error type', () => {
      const content = 'Error Message';
      service.errorMessage(content);
      expect(messageServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.error,
        content,
      );
    });

    it('should call messageService.create with warning type', () => {
      const content = 'Warning Message';
      service.warningMessage(content);
      expect(messageServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.warning,
        content,
      );
    });

    it('should call messageService.create with info type', () => {
      const content = 'Info Message';
      service.infoMessage(content);
      expect(messageServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.info,
        content,
      );
    });

    it('should call messageService.create with blank type', () => {
      const content = 'Blank Message';
      service.blankMessage(content);
      expect(messageServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.blank,
        content,
      );
    });
  });

  describe('Loader Message', () => {
    it('should show loader message and return messageId', () => {
      const dummyMessageId = 'loader123';
      const mockMessageRef = {
        messageId: dummyMessageId,
        onClose: new Subject<boolean>(),
      };

      messageServiceSpy.loading.and.returnValue(mockMessageRef);

      const returnedMessageId = service.showLoaderMessage('Loading...');
      expect(messageServiceSpy.loading).toHaveBeenCalledWith('Loading...');
      expect(returnedMessageId).toBe(dummyMessageId);
    });

    it('should remove loader message by messageId', () => {
      const messageId = 'loader123';
      service.hideLoaderMessage(messageId);
      expect(messageServiceSpy.remove).toHaveBeenCalledWith(messageId);
    });
  });
});